2025/08/01-11:05:27.434 4b28 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.cd195ffa\flutter_tools_chrome_device.7e08ca66\Default\IndexedDB\http_localhost_2031.indexeddb.leveldb/MANIFEST-000001
2025/08/01-11:05:27.435 4b28 Recovering log #7
2025/08/01-11:05:27.437 4b28 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.cd195ffa\flutter_tools_chrome_device.7e08ca66\Default\IndexedDB\http_localhost_2031.indexeddb.leveldb/000007.log 
2025/08/01-11:05:27.459 3854 Level-0 table #12: started
2025/08/01-11:05:27.461 3854 Level-0 table #12: 3364 bytes OK
2025/08/01-11:05:27.464 3854 Delete type=0 #7
2025/08/01-11:05:27.465 4be0 Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/01-11:06:47.474 4b28 Compacting 1@1 + 1@2 files
2025/08/01-11:06:47.479 4b28 Generated table #13@1: 41 keys, 3027 bytes
2025/08/01-11:06:47.479 4b28 Compacted 1@1 + 1@2 files => 3027 bytes
2025/08/01-11:06:47.483 4b28 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/01-11:06:47.483 4b28 Delete type=2 #9
2025/08/01-11:06:47.483 4b28 Delete type=2 #12

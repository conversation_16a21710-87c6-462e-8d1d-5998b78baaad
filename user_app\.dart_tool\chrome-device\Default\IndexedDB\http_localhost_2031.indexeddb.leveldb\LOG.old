2025/08/01-10:45:54.104 4b28 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.cd195ffa\flutter_tools_chrome_device.7e08ca66\Default\IndexedDB\http_localhost_2031.indexeddb.leveldb/MANIFEST-000001
2025/08/01-10:45:54.105 4b28 Recovering log #4
2025/08/01-10:45:54.106 4b28 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.cd195ffa\flutter_tools_chrome_device.7e08ca66\Default\IndexedDB\http_localhost_2031.indexeddb.leveldb/000004.log 
2025/08/01-10:45:54.125 3854 Level-0 table #8: started
2025/08/01-10:45:54.127 3854 Level-0 table #8: 4653 bytes OK
2025/08/01-10:45:54.130 3854 Delete type=0 #4
2025/08/01-10:45:54.131 4be0 Manual compaction at level-0 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/01-10:47:12.657 4b28 Compacting 1@1 + 1@2 files
2025/08/01-10:47:14.468 4b28 Generated table #9@1: 40 keys, 3008 bytes
2025/08/01-10:47:14.468 4b28 Compacted 1@1 + 1@2 files => 3008 bytes
2025/08/01-10:47:15.023 4b28 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/01-10:47:15.120 4b28 Delete type=2 #5
2025/08/01-10:47:15.120 4b28 Delete type=2 #8

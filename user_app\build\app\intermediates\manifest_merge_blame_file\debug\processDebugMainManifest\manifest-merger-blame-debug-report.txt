1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.amalpoint.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:3:5-67
15-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Camera and storage permissions for image picker -->
16-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:4:5-79
16-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:4:22-76
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:7:5-65
17-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:7:22-62
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:8:5-80
18-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:8:22-77
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:9:5-81
19-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:9:22-78
20    <!--
21 Required to query activities that can process text, see:
22         https://developer.android.com/training/package-visibility and
23         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
24
25         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
26    -->
27    <queries>
27-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:49:5-70:15
28        <intent>
28-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:50:9-53:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:51:13-72
29-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:51:21-70
30
31            <data android:mimeType="text/plain" />
31-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
31-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:19-48
32        </intent>
33        <!-- Query for WhatsApp -->
34        <package android:name="com.whatsapp" />
34-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:55:9-48
34-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:55:18-45
35        <package android:name="com.whatsapp.w4b" />
35-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:56:9-52
35-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:56:18-49
36        <!-- Query for WeChat -->
37        <package android:name="com.tencent.mm" />
37-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:58:9-50
37-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:58:18-47
38        <!-- Query for web browsers -->
39        <intent>
39-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:60:9-64:18
40            <action android:name="android.intent.action.VIEW" />
40-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
40-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
41
42            <category android:name="android.intent.category.BROWSABLE" />
42-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
42-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
43
44            <data android:scheme="https" />
44-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
45        </intent>
46        <intent>
46-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:65:9-69:18
47            <action android:name="android.intent.action.VIEW" />
47-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
47-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
48
49            <category android:name="android.intent.category.BROWSABLE" />
49-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
49-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
50
51            <data android:scheme="http" />
51-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
52        </intent>
53    </queries>
54
55    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
55-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\772d5e5ffa60b06272bcc2af1d02beb4\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
55-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\772d5e5ffa60b06272bcc2af1d02beb4\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
56
57    <permission
57-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
58        android:name="com.amalpoint.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
58-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
59        android:protectionLevel="signature" />
59-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
60
61    <uses-permission android:name="com.amalpoint.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
61-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
61-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
62
63    <application
64        android:name="android.app.Application"
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
66        android:debuggable="true"
67        android:extractNativeLibs="false"
68        android:icon="@mipmap/ic_launcher"
69        android:label="Amal Point"
70        android:testOnly="true"
71        android:usesCleartextTraffic="true" >
72        <activity
73            android:name="com.amalpoint.app.MainActivity"
74            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
75            android:exported="true"
76            android:hardwareAccelerated="true"
77            android:launchMode="singleTop"
78            android:taskAffinity=""
79            android:theme="@style/LaunchTheme"
80            android:windowSoftInputMode="adjustResize" >
81
82            <!--
83                 Specifies an Android theme to apply to this Activity as soon as
84                 the Android process has started. This theme is visible to the user
85                 while the Flutter UI initializes. After that, this theme continues
86                 to determine the Window background behind the Flutter UI.
87            -->
88            <meta-data
89                android:name="io.flutter.embedding.android.NormalTheme"
90                android:resource="@style/NormalTheme" />
91
92            <intent-filter>
93                <action android:name="android.intent.action.MAIN" />
94
95                <category android:name="android.intent.category.LAUNCHER" />
96            </intent-filter>
97        </activity>
98        <!--
99             Don't delete the meta-data below.
100             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
101        -->
102        <meta-data
103            android:name="flutterEmbedding"
104            android:value="2" />
105
106        <service
106-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
107            android:name="com.google.firebase.components.ComponentDiscoveryService"
107-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
108            android:directBootAware="true"
108-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
109            android:exported="false" >
109-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
110            <meta-data
110-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
111                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
111-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
113            <meta-data
113-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
114                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
114-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
116            <meta-data
116-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
117                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
117-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
119            <meta-data
119-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
120                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
120-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
122            <meta-data
122-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
123                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
123-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
125            <meta-data
125-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
126                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
126-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
128            <meta-data
128-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
129                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
129-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
131            <meta-data
131-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
132                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
132-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
134            <meta-data
134-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
135                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
135-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
137            <meta-data
137-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
138                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
138-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
140            <meta-data
140-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
141                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
141-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
143            <meta-data
143-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
144                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
144-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
146            <meta-data
146-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
147                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
147-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
149        </service>
150
151        <provider
151-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
152            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
152-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
153            android:authorities="com.amalpoint.app.flutter.image_provider"
153-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
154            android:exported="false"
154-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
155            android:grantUriPermissions="true" >
155-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
156            <meta-data
156-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
157                android:name="android.support.FILE_PROVIDER_PATHS"
157-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
158                android:resource="@xml/flutter_image_picker_file_paths" />
158-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
159        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
160        <service
160-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
161            android:name="com.google.android.gms.metadata.ModuleDependencies"
161-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
162            android:enabled="false"
162-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
163            android:exported="false" >
163-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
164            <intent-filter>
164-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
165                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
165-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
165-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
166            </intent-filter>
167
168            <meta-data
168-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
169                android:name="photopicker_activity:0:required"
169-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
170                android:value="" />
170-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
171        </service>
172        <!--
173           Declares a provider which allows us to store files to share in
174           '.../caches/share_plus' and grant the receiving action access
175        -->
176        <provider
176-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
177            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
177-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
178            android:authorities="com.amalpoint.app.flutter.share_provider"
178-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
179            android:exported="false"
179-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
180            android:grantUriPermissions="true" >
180-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
181            <meta-data
181-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
182                android:name="android.support.FILE_PROVIDER_PATHS"
182-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
183                android:resource="@xml/flutter_share_file_paths" />
183-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
184        </provider>
185        <!--
186           This manifest declared broadcast receiver allows us to use an explicit
187           Intent when creating a PendingItent to be informed of the user's choice
188        -->
189        <receiver
189-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
190            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
190-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
191            android:exported="false" >
191-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
192            <intent-filter>
192-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
193                <action android:name="EXTRA_CHOSEN_COMPONENT" />
193-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
193-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
194            </intent-filter>
195        </receiver>
196
197        <activity
197-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
198            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
198-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
199            android:exported="false"
199-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
200            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
200-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
201        <activity
201-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
202            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
202-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
203            android:excludeFromRecents="true"
203-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
204            android:exported="true"
204-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
205            android:launchMode="singleTask"
205-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
206            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
206-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
207            <intent-filter>
207-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
208                <action android:name="android.intent.action.VIEW" />
208-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
208-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
209
210                <category android:name="android.intent.category.DEFAULT" />
210-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
210-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
211                <category android:name="android.intent.category.BROWSABLE" />
211-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
211-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
212
213                <data
213-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
214                    android:host="firebase.auth"
215                    android:path="/"
216                    android:scheme="genericidp" />
217            </intent-filter>
218        </activity>
219        <activity
219-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
220            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
220-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
221            android:excludeFromRecents="true"
221-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
222            android:exported="true"
222-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
223            android:launchMode="singleTask"
223-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
224            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
224-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
225            <intent-filter>
225-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
226                <action android:name="android.intent.action.VIEW" />
226-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
226-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
227
228                <category android:name="android.intent.category.DEFAULT" />
228-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
228-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
229                <category android:name="android.intent.category.BROWSABLE" />
229-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
229-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
230
231                <data
231-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
232                    android:host="firebase.auth"
233                    android:path="/"
234                    android:scheme="recaptcha" />
235            </intent-filter>
236        </activity>
237
238        <provider
238-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
239            android:name="com.google.firebase.provider.FirebaseInitProvider"
239-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
240            android:authorities="com.amalpoint.app.firebaseinitprovider"
240-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
241            android:directBootAware="true"
241-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
242            android:exported="false"
242-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
243            android:initOrder="100" />
243-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
244
245        <service
245-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
246            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
246-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
247            android:enabled="true"
247-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
248            android:exported="false" >
248-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
249            <meta-data
249-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
250                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
250-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
251                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
251-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
252        </service>
253
254        <activity
254-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
255            android:name="androidx.credentials.playservices.HiddenActivity"
255-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
256            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
256-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
257            android:enabled="true"
257-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
258            android:exported="false"
258-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
259            android:fitsSystemWindows="true"
259-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
260            android:theme="@style/Theme.Hidden" >
260-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
261        </activity>
262        <activity
262-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
263            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
263-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
264            android:excludeFromRecents="true"
264-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
265            android:exported="false"
265-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
266            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
266-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
267        <!--
268            Service handling Google Sign-In user revocation. For apps that do not integrate with
269            Google Sign-In, this service will never be started.
270        -->
271        <service
271-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
272            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
272-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
273            android:exported="true"
273-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
274            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
274-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
275            android:visibleToInstantApps="true" />
275-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
276
277        <activity
277-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
278            android:name="com.google.android.gms.common.api.GoogleApiActivity"
278-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
279            android:exported="false"
279-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
280            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
280-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
281
282        <provider
282-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
283            android:name="androidx.startup.InitializationProvider"
283-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
284            android:authorities="com.amalpoint.app.androidx-startup"
284-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
285            android:exported="false" >
285-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
286            <meta-data
286-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
287                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
287-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
288                android:value="androidx.startup" />
288-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
289            <meta-data
289-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
290                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
290-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
291                android:value="androidx.startup" />
291-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
292        </provider>
293
294        <uses-library
294-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
295            android:name="androidx.window.extensions"
295-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
296            android:required="false" />
296-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
297        <uses-library
297-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
298            android:name="androidx.window.sidecar"
298-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
299            android:required="false" />
299-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
300
301        <meta-data
301-->[com.google.android.gms:play-services-basement:18.4.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
302            android:name="com.google.android.gms.version"
302-->[com.google.android.gms:play-services-basement:18.4.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
303            android:value="@integer/google_play_services_version" />
303-->[com.google.android.gms:play-services-basement:18.4.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
304
305        <receiver
305-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
306            android:name="androidx.profileinstaller.ProfileInstallReceiver"
306-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
307            android:directBootAware="false"
307-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
308            android:enabled="true"
308-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
309            android:exported="true"
309-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
310            android:permission="android.permission.DUMP" >
310-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
311            <intent-filter>
311-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
312                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
313            </intent-filter>
314            <intent-filter>
314-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
315                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
316            </intent-filter>
317            <intent-filter>
317-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
318                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
319            </intent-filter>
320            <intent-filter>
320-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
321                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
322            </intent-filter>
323        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
324        <activity
324-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
325            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
325-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
326            android:exported="false"
326-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
327            android:stateNotNeeded="true"
327-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
328            android:theme="@style/Theme.PlayCore.Transparent" />
328-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
329    </application>
330
331</manifest>

import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Admin App';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get notFound => 'Page Not Found';

  @override
  String get goToDashboard => 'Go to Dashboard';
}
